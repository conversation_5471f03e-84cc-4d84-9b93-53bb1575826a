<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>文件上传测试</title>
</head>
<body>
    <h1>文件上传测试</h1>
    <input type="file" id="fileInput" accept=".csv" />
    <button onclick="testUpload()">测试上传</button>
    <div id="result"></div>
    
    <script>
        function testUpload() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                document.getElementById('result').innerHTML = '请先选择文件';
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const csvText = e.target.result;
                console.log('文件内容:', csvText);
                document.getElementById('result').innerHTML = '文件读取成功，长度: ' + csvText.length;
            };
            reader.readAsText(file, 'UTF-8');
        }
        
        document.getElementById('fileInput').addEventListener('change', function(e) {
            console.log('文件选择事件触发');
            testUpload();
        });
    </script>
</body>
</html>