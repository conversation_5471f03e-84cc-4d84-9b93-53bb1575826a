#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地图连接测试工具
用于诊断客户环境的地图显示问题
"""

import requests
import time
import sys
from urllib.parse import urlparse

def test_url(url, description):
    """测试URL连接"""
    print(f"\n测试 {description}...")
    print(f"URL: {url}")
    
    try:
        start_time = time.time()
        response = requests.get(url, timeout=10, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        end_time = time.time()
        
        if response.status_code == 200:
            print(f"✅ 成功 - 状态码: {response.status_code}, 响应时间: {end_time-start_time:.2f}秒")
            print(f"   内容长度: {len(response.content)} 字节")
            return True
        else:
            print(f"❌ 失败 - 状态码: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 超时 - 连接超过10秒")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ 连接错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("大理州通信故障地图分析系统 - 网络连接测试")
    print("=" * 60)
    
    # 测试地图服务
    map_services = [
        ("https://tile.openstreetmap.org/0/0/0.png", "OpenStreetMap 主服务"),
        ("https://a.tile.openstreetmap.org/0/0/0.png", "OpenStreetMap 镜像A"),
        ("https://b.tile.openstreetmap.org/0/0/0.png", "OpenStreetMap 镜像B"),
        ("https://c.tile.openstreetmap.org/0/0/0.png", "OpenStreetMap 镜像C"),
        ("https://cartodb-basemaps-a.global.ssl.fastly.net/light_all/0/0/0.png", "CartoDB 地图服务"),
    ]
    
    # 测试JavaScript库
    js_libraries = [
        ("https://unpkg.com/leaflet@1.9.4/dist/leaflet.css", "Leaflet CSS库"),
        ("https://unpkg.com/leaflet@1.9.4/dist/leaflet.js", "Leaflet JavaScript库"),
        ("https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js", "Leaflet 热力图插件"),
    ]
    
    print("\n🗺️ 测试地图瓦片服务...")
    map_success = 0
    for url, desc in map_services:
        if test_url(url, desc):
            map_success += 1
    
    print("\n📚 测试JavaScript库...")
    js_success = 0
    for url, desc in js_libraries:
        if test_url(url, desc):
            js_success += 1
    
    # 测试结果总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    print(f"地图服务: {map_success}/{len(map_services)} 可用")
    print(f"JavaScript库: {js_success}/{len(js_libraries)} 可用")
    
    if map_success == 0:
        print("\n❌ 严重问题：所有地图服务都无法访问")
        print("可能原因：")
        print("1. 网络连接问题")
        print("2. 防火墙阻止了地图服务")
        print("3. 企业网络限制")
        print("4. DNS解析问题")
        
        print("\n建议解决方案：")
        print("1. 检查网络连接")
        print("2. 联系IT管理员开放地图服务域名")
        print("3. 尝试使用VPN或代理")
        print("4. 使用移动热点测试")
        
    elif map_success < len(map_services):
        print(f"\n⚠️ 部分地图服务不可用 ({map_success}/{len(map_services)})")
        print("系统会自动切换到可用的服务")
        
    else:
        print("\n✅ 所有地图服务都可用，地图应该能正常显示")
    
    if js_success < len(js_libraries):
        print(f"\n⚠️ 部分JavaScript库不可用 ({js_success}/{len(js_libraries)})")
        print("这可能影响地图功能")
    
    print("\n" + "=" * 60)
    input("按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中出现错误: {e}")
        input("按回车键退出...")
