#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的文件上传功能测试
"""

import os
import time
import subprocess
import requests
import webbrowser
from threading import Thread

def start_server():
    """启动服务器"""
    print("🚀 启动服务器...")
    process = subprocess.Popen(['python', 'map_system_app.py'])
    time.sleep(3)
    
    # 检查服务器是否启动
    try:
        response = requests.get('http://localhost:8080', timeout=5)
        print(f"✅ 服务器启动成功，状态码: {response.status_code}")
        return process
    except:
        print("❌ 服务器启动失败")
        return None

def create_test_files():
    """创建测试文件"""
    print("📁 创建测试文件...")
    
    # CSV测试文件
    csv_content = """序号,区域,故障系统（断点经纬度）,故障级别,故障段落,故障发生时间,故障原因,故障详细原因备注
1,宾川县,"经度：100.451122
纬度：25.771616",汇聚层,SPN 100G 环 宾川县 大营瑶草庄-宾川县州城,2025/1/1 11:50,车辆挂断,瑶草庄出局3.2km挖机挂断光缆
2,鹤庆县,"经度：100.157276
纬度：26.538180",汇聚层,OTN 老平面 鹤庆新移动（东北环）-洱源牛街上站,2025/1/1 12:35,车辆挂断,新移动出局5.46km，附挂联通杆路超高车辆挂断
3,弥渡县,"经度：100.454857
纬度：25.336857",接入层,PTN 弥渡县弥城双树村 6220设备脱管,2025/1/1 13:40,人为破坏,蔡庄出局3km自来水厂修管道夹断导致"""
    
    with open("test_upload.csv", "w", encoding="utf-8") as f:
        f.write(csv_content)
    
    # 简单格式的CSV文件
    simple_csv = """序号,区域,经度,纬度,故障级别,故障原因
1,大理市,100.203212,25.593299,接入层,车辆挂断
2,祥云县,100.5,25.5,汇聚层,设备故障"""
    
    with open("test_simple.csv", "w", encoding="utf-8") as f:
        f.write(simple_csv)
    
    print("✅ 测试文件创建完成")

def test_csv_parsing():
    """测试CSV解析功能"""
    print("🧪 测试CSV解析功能...")
    
    # 读取app.js中的parseCSV函数逻辑，用Python重现
    def parse_csv_python(csv_text):
        lines = csv_text.split('\n')
        if len(lines) < 2:
            return []
        
        # 检测分隔符
        first_line = lines[0]
        separator = '\t' if '\t' in first_line else ','
        print(f"检测到分隔符: {'制表符' if separator == '\t' else '逗号'}")
        
        headers = first_line.split(separator)
        print(f"CSV标题行: {headers}")
        
        data = []
        for i in range(1, len(lines)):
            if lines[i].strip():
                values = lines[i].split(separator)
                row = {}
                for j, header in enumerate(headers):
                    row[header.strip()] = values[j].strip() if j < len(values) else ''
                
                # 解析经纬度
                coord_text = row.get('故障系统（断点经纬度）', '')
                if coord_text:
                    import re
                    lon_match = re.search(r'经度：([\d.]+)', coord_text)
                    lat_match = re.search(r'纬度：([\d.]+)', coord_text)
                    
                    if lon_match and lat_match:
                        row['longitude'] = float(lon_match.group(1))
                        row['latitude'] = float(lat_match.group(1))
                else:
                    # 尝试其他格式
                    lon = row.get('经度') or row.get('longitude') or row.get('lng')
                    lat = row.get('纬度') or row.get('latitude') or row.get('lat')
                    
                    try:
                        if lon and lat:
                            row['longitude'] = float(lon)
                            row['latitude'] = float(lat)
                    except:
                        continue
                
                # 检查坐标范围
                if 'longitude' in row and 'latitude' in row:
                    if 99 <= row['longitude'] <= 101 and 24.5 <= row['latitude'] <= 27:
                        data.append(row)
        
        return data
    
    # 测试复杂格式CSV
    print("\n测试复杂格式CSV:")
    with open("test_upload.csv", "r", encoding="utf-8") as f:
        csv_content = f.read()
    
    data1 = parse_csv_python(csv_content)
    print(f"解析结果: {len(data1)} 条数据")
    for item in data1:
        print(f"  - {item.get('区域')}: ({item.get('longitude')}, {item.get('latitude')})")
    
    # 测试简单格式CSV
    print("\n测试简单格式CSV:")
    with open("test_simple.csv", "r", encoding="utf-8") as f:
        csv_content = f.read()
    
    data2 = parse_csv_python(csv_content)
    print(f"解析结果: {len(data2)} 条数据")
    for item in data2:
        print(f"  - {item.get('区域')}: ({item.get('longitude')}, {item.get('latitude')})")
    
    return len(data1) > 0 and len(data2) > 0

def create_test_html():
    """创建测试HTML页面"""
    print("📄 创建测试HTML页面...")
    
    html_content = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>文件上传测试</title>
</head>
<body>
    <h1>文件上传测试</h1>
    <input type="file" id="fileInput" accept=".csv" />
    <button onclick="testUpload()">测试上传</button>
    <div id="result"></div>
    
    <script>
        function testUpload() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                document.getElementById('result').innerHTML = '请先选择文件';
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const csvText = e.target.result;
                console.log('文件内容:', csvText);
                document.getElementById('result').innerHTML = '文件读取成功，长度: ' + csvText.length;
            };
            reader.readAsText(file, 'UTF-8');
        }
        
        document.getElementById('fileInput').addEventListener('change', function(e) {
            console.log('文件选择事件触发');
            testUpload();
        });
    </script>
</body>
</html>"""
    
    with open("test_upload.html", "w", encoding="utf-8") as f:
        f.write(html_content)
    
    print("✅ 测试HTML页面创建完成")

def main():
    print("=" * 60)
    print("大理州通信故障地图分析系统 - 文件上传功能测试")
    print("=" * 60)
    
    # 创建测试文件
    create_test_files()
    
    # 测试CSV解析
    parse_success = test_csv_parsing()
    
    # 创建测试页面
    create_test_html()
    
    # 启动服务器
    server_process = start_server()
    
    if server_process:
        print("\n🌐 打开浏览器进行手动测试...")
        print("请在浏览器中:")
        print("1. 访问 http://localhost:8080")
        print("2. 点击'📁 选择文件'按钮")
        print("3. 选择 test_upload.csv 或 test_simple.csv")
        print("4. 查看是否显示'成功导入 X 条数据'")
        print("5. 查看地图上是否显示标记点")
        
        # 自动打开浏览器
        webbrowser.open('http://localhost:8080')
        
        print("\n按回车键停止服务器...")
        input()
        
        server_process.terminate()
        print("🔄 服务器已停止")
    
    print(f"\n📊 测试结果:")
    print(f"  - CSV解析功能: {'✅ 通过' if parse_success else '❌ 失败'}")
    print(f"  - 服务器启动: {'✅ 通过' if server_process else '❌ 失败'}")

if __name__ == "__main__":
    main()
