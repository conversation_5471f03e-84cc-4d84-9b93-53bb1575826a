大理州通信故障地图分析系统 - 使用说明

=== 快速开始 ===

1. 双击运行：大理州通信故障地图分析系统.exe
2. 点击：🚀 启动地图系统
3. 程序自动打开浏览器显示地图
4. 点击：加载示例数据

=== 主要功能 ===

• 数据导入：支持CSV格式数据导入
• 地图可视化：交互式地图显示故障点
• 数据分析：故障统计和趋势分析
• 数据导出：支持分析结果导出

=== 系统要求 ===

• Windows 7 或更高版本
• 2GB 内存
• 现代浏览器（Chrome、Firefox、Edge）
• 网络连接

=== 地图显示问题解决方案 ===

如果地图不显示，请按以下步骤操作：

方案一：使用Python直接运行
1. 确保已安装Python 3.7+
2. 在项目目录打开命令行
3. 运行：python map_system_app.py
4. 这样可以看到详细的错误信息

方案二：检查网络连接
1. 确保能正常访问互联网
2. 尝试在浏览器中访问：https://tile.openstreetmap.org/0/0/0.png
3. 如果无法访问，说明网络限制了地图服务

方案三：企业网络环境
1. 联系IT管理员开放以下域名：
   - tile.openstreetmap.org
   - basemaps.cartocdn.com
   - unpkg.com (用于加载Leaflet库)

方案四：使用本地服务器
1. 安装Node.js
2. 在项目目录运行：npx http-server -p 8080
3. 浏览器访问：http://localhost:8080

=== 常见问题 ===

问题：程序启动后浏览器没有自动打开
解决：手动打开浏览器，访问程序窗口显示的地址

问题：端口被占用
解决：程序会自动切换到其他可用端口

问题：地图显示空白
解决：按照上述"地图显示问题解决方案"操作

=== 文件说明 ===

• 大理州通信故障地图分析系统.exe - 主程序
• sample_data.csv - 示例数据
• 数据导入模板.csv - 数据格式模板
• 使用说明.txt - 本说明文件
