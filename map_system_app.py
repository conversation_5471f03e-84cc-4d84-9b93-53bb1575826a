#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大理州通信故障地图分析系统 - 桌面应用程序
集成Web服务器和浏览器启动功能
"""

import os
import sys
import time
import threading
import webbrowser
import http.server
import socketserver
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import subprocess
import shutil
import traceback
import socket

class MapSystemApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("大理州通信故障地图分析系统")
        self.root.geometry("600x400")
        self.root.resizable(False, False)
        
        # 设置图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        self.server = None
        self.server_thread = None
        self.port = 8080
        self.available_ports = [8080, 8081, 8082, 3000, 5000, 9000]

        
        self.setup_ui()
        self.copy_web_files()
        self.check_files()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(pady=20)
        
        title_label = ttk.Label(title_frame, text="大理州通信故障地图分析系统", 
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame, text="通信故障地图可视化分析工具", 
                                  font=("Microsoft YaHei", 10))
        subtitle_label.pack(pady=(5, 0))
        
        # 状态显示
        status_frame = ttk.LabelFrame(self.root, text="系统状态", padding=10)
        status_frame.pack(fill="x", padx=20, pady=10)
        
        self.status_label = ttk.Label(status_frame, text="系统已就绪", 
                                     font=("Microsoft YaHei", 10))
        self.status_label.pack()
        
        # 功能按钮
        button_frame = ttk.Frame(self.root)
        button_frame.pack(pady=20)
        
        # 启动系统按钮
        self.start_btn = ttk.Button(button_frame, text="🚀 启动地图系统",
                                   command=self.start_system, width=20)
        self.start_btn.pack(pady=5)



        # 停止系统按钮
        self.stop_btn = ttk.Button(button_frame, text="⏹️ 停止系统",
                                  command=self.stop_system, width=20, state="disabled")
        self.stop_btn.pack(pady=5)

        # 打开浏览器按钮
        self.browser_btn = ttk.Button(button_frame, text="🌐 打开浏览器",
                                     command=self.open_browser, width=20, state="disabled")
        self.browser_btn.pack(pady=5)
        
        # 打开文件夹按钮
        self.folder_btn = ttk.Button(button_frame, text="📁 打开系统文件夹", 
                                    command=self.open_folder, width=20)
        self.folder_btn.pack(pady=5)
        
        # 帮助按钮
        self.help_btn = ttk.Button(button_frame, text="❓ 使用帮助", 
                                  command=self.show_help, width=20)
        self.help_btn.pack(pady=5)
        
        # 底部信息
        info_frame = ttk.Frame(self.root)
        info_frame.pack(side="bottom", fill="x", padx=20, pady=10)
        
        info_label = ttk.Label(info_frame, text="版本 1.0 | 支持CSV/Excel数据导入 | 响应式地图可视化", 
                              font=("Microsoft YaHei", 8), foreground="gray")
        info_label.pack()

    def copy_web_files(self):
        """复制web文件到执行目录（用于打包后的程序）"""
        if hasattr(sys, '_MEIPASS'):
            # 打包后的环境，需要复制文件
            source_dir = sys._MEIPASS
            target_dir = os.getcwd()  # 这应该是exe文件所在的目录



            web_files = ['index.html', 'app.js', 'sample_data.csv', '数据导入模板.csv']

            for file_name in web_files:
                source_path = os.path.join(source_dir, file_name)
                target_path = os.path.join(target_dir, file_name)

                try:
                    if os.path.exists(source_path):
                        # 总是复制文件，覆盖已存在的文件以确保是最新版本
                        shutil.copy2(source_path, target_path)
                except Exception as e:
                    pass  # 静默处理错误

    def find_available_port(self):
        """查找可用端口"""
        import socket

        for port in self.available_ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex(('127.0.0.1', port))
                sock.close()

                if result != 0:  # 端口未被占用
                    return port
            except Exception as e:
                continue

        # 如果所有预设端口都被占用，尝试系统分配
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.bind(('127.0.0.1', 0))
            port = sock.getsockname()[1]
            sock.close()
            return port
        except Exception as e:
            return 8080  # 回退到默认端口

    def check_files(self):
        """检查必要文件"""
        required_files = ["index.html", "app.js", "sample_data.csv"]
        missing_files = []

        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)

        if missing_files:
            if hasattr(sys, '_MEIPASS'):
                # 打包环境下，尝试从资源中复制缺失的文件
                for file in missing_files[:]:  # 使用切片复制列表以避免修改迭代中的列表
                    source_path = os.path.join(sys._MEIPASS, file)
                    if os.path.exists(source_path):
                        try:
                            shutil.copy2(source_path, file)
                            missing_files.remove(file)
                            print(f"已从资源复制文件: {file}")
                        except Exception as e:
                            print(f"复制文件 {file} 失败: {e}")

            if missing_files:
                self.status_label.config(text=f"缺少文件: {', '.join(missing_files)}",
                                       foreground="red")
                self.start_btn.config(state="disabled")
            else:
                self.status_label.config(text="系统文件完整，可以启动", foreground="green")
        else:
            self.status_label.config(text="系统文件完整，可以启动", foreground="green")
    
    def start_system(self):
        """启动系统"""
        try:
            # 再次检查文件是否存在
            if not os.path.exists("index.html"):
                messagebox.showerror("启动失败", "找不到index.html文件，请检查文件是否完整")
                return

            # 查找可用端口
            available_port = self.find_available_port()
            if available_port != self.port:
                self.port = available_port

            # 启动HTTP服务器
            handler = http.server.SimpleHTTPRequestHandler
            try:
                self.server = socketserver.TCPServer(("", self.port), handler)
            except OSError as e:
                if "Address already in use" in str(e):
                    # 重新查找端口
                    self.port = self.find_available_port()
                    self.server = socketserver.TCPServer(("", self.port), handler)
                else:
                    raise e

            # 在新线程中运行服务器
            self.server_thread = threading.Thread(target=self.server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()

            self.status_label.config(text=f"服务器已启动 - http://localhost:{self.port}",
                                   foreground="green")

            # 更新按钮状态
            self.start_btn.config(state="disabled")
            self.stop_btn.config(state="normal")
            self.browser_btn.config(state="normal")

            # 自动打开浏览器
            time.sleep(1)
            self.open_browser()

        except Exception as e:
            messagebox.showerror("启动失败", f"无法启动服务器: {str(e)}")


    
    def stop_system(self):
        """停止系统"""
        if self.server:
            self.server.shutdown()
            self.server = None
            
        self.status_label.config(text="服务器已停止", foreground="orange")
        
        # 更新按钮状态
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.browser_btn.config(state="disabled")
    
    def open_browser(self):
        """打开浏览器"""
        try:
            url = f"http://localhost:{self.port}"
            webbrowser.open(url)
        except Exception as e:
            messagebox.showerror("打开失败", f"无法打开浏览器: {str(e)}")
    
    def open_folder(self):
        """打开系统文件夹"""
        try:
            if sys.platform == "win32":
                os.startfile(".")
            elif sys.platform == "darwin":
                subprocess.run(["open", "."])
            else:
                subprocess.run(["xdg-open", "."])
        except Exception as e:
            messagebox.showerror("打开失败", f"无法打开文件夹: {str(e)}")
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
大理州通信故障地图分析系统 - 使用帮助

🚀 快速开始:
1. 点击"启动地图系统"按钮
2. 系统会自动打开浏览器
3. 点击"加载示例数据"查看演示

📊 主要功能:
• 交互式地图显示故障位置
• 支持CSV和Excel数据导入
• 多维度数据筛选功能
• 关键词搜索功能
• 热力图模式显示故障密度
• 数据导出功能

📁 数据格式:
• 支持CSV和Excel文件
• 必需包含经纬度信息
• 参考"数据导入模板.csv"

🔧 技术要求:
• 需要现代浏览器支持
• 需要网络连接加载地图
• 建议使用Chrome或Firefox

❓ 如有问题:
• 检查网络连接
• 确保浏览器支持JavaScript
• 查看"完整使用指南.md"
        """
        
        help_window = tk.Toplevel(self.root)
        help_window.title("使用帮助")
        help_window.geometry("500x600")
        help_window.resizable(False, False)
        
        text_widget = tk.Text(help_window, wrap=tk.WORD, padx=10, pady=10)
        text_widget.pack(fill="both", expand=True)
        text_widget.insert("1.0", help_text)
        text_widget.config(state="disabled")
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(help_window, orient="vertical", command=text_widget.yview)
        scrollbar.pack(side="right", fill="y")
        text_widget.config(yscrollcommand=scrollbar.set)
    
    def on_closing(self):
        """关闭程序时的处理"""
        if self.server:
            self.stop_system()
        self.root.destroy()
    
    def run(self):
        """运行应用程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """主函数"""
    # 确保在正确的目录中运行
    if hasattr(sys, '_MEIPASS'):
        # PyInstaller打包后的环境
        # 保持在exe文件所在的目录，而不是临时目录
        exe_dir = os.path.dirname(os.path.abspath(sys.executable))
        os.chdir(exe_dir)
    else:
        # 开发环境
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)

    app = MapSystemApp()
    app.run()

if __name__ == "__main__":
    main()
