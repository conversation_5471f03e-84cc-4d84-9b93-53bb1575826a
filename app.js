// 全局变量
let map;
let markersLayer;
let heatmapLayer;
let allData = [];
let filteredData = [];
let currentMapMode = 'markers';

// 地图源配置（优先使用国内可访问的服务）
const mapSources = [
    {
        name: '高德地图',
        url: 'https://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',
        attribution: '© 高德地图'
    },
    {
        name: '高德地图(备用)',
        url: 'https://webrd02.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',
        attribution: '© 高德地图'
    },
    {
        name: 'OpenStreetMap',
        url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
        attribution: '© OpenStreetMap contributors'
    },
    {
        name: 'OpenStreetMap (备用)',
        url: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
        attribution: '© OpenStreetMap contributors'
    },
    {
        name: 'CartoDB',
        url: 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png',
        attribution: '© OpenStreetMap © CartoDB'
    }
];

let currentMapSourceIndex = 0;
let mapLayer = null;

// 初始化地图
function initMap() {
    // 大理州中心坐标
    map = L.map('map').setView([25.6, 100.3], 9);

    // 尝试加载地图图层
    loadMapLayer();

    // 初始化图层组
    markersLayer = L.layerGroup().addTo(map);
    heatmapLayer = L.layerGroup();
}

// 加载地图图层
function loadMapLayer() {
    const source = mapSources[currentMapSourceIndex];

    // 移除现有图层
    if (mapLayer) {
        map.removeLayer(mapLayer);
    }

    // 创建新图层
    mapLayer = L.tileLayer(source.url, {
        attribution: source.attribution,
        timeout: 10000 // 10秒超时
    });

    // 监听图层加载事件
    mapLayer.on('tileerror', function(e) {
        console.warn(`地图源 ${source.name} 加载失败，尝试下一个源...`);
        tryNextMapSource();
    });

    mapLayer.on('tileload', function(e) {
        console.log(`地图源 ${source.name} 加载成功`);
        showMapStatus(`地图加载成功 (${source.name})`);
    });

    // 添加到地图
    mapLayer.addTo(map);

    // 显示当前尝试的地图源
    showMapStatus(`正在加载地图 (${source.name})...`);
}

// 尝试下一个地图源
function tryNextMapSource() {
    currentMapSourceIndex++;
    if (currentMapSourceIndex < mapSources.length) {
        setTimeout(() => {
            loadMapLayer();
        }, 2000); // 等待2秒后尝试下一个源
    } else {
        // 所有地图源都失败了
        showMapStatus('地图加载失败，请检查网络连接', 'error');
        showOfflineMode();
    }
}

// 显示地图状态
function showMapStatus(message, type = 'info') {
    const statusDiv = document.getElementById('map-status') || createMapStatusDiv();
    statusDiv.textContent = message;
    statusDiv.className = `map-status ${type}`;
    statusDiv.style.display = 'block';

    if (type === 'info') {
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 3000);
    }
}

// 创建地图状态显示元素
function createMapStatusDiv() {
    const statusDiv = document.createElement('div');
    statusDiv.id = 'map-status';
    statusDiv.style.cssText = `
        position: absolute;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1000;
        padding: 10px 20px;
        border-radius: 5px;
        font-weight: bold;
        text-align: center;
        min-width: 200px;
    `;
    document.getElementById('map').appendChild(statusDiv);
    return statusDiv;
}

// 显示离线模式
function showOfflineMode() {
    const mapContainer = document.getElementById('map');
    const offlineDiv = document.createElement('div');
    offlineDiv.innerHTML = `
        <div style="
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            z-index: 1000;
        ">
            <h3>🗺️ 地图服务不可用</h3>
            <p>无法连接到地图服务，可能原因：</p>
            <ul style="text-align: left; margin: 15px 0;">
                <li>网络连接问题</li>
                <li>防火墙阻止了地图服务</li>
                <li>企业网络限制</li>
            </ul>
            <p><strong>数据分析功能仍然可用</strong></p>
            <button onclick="retryMapLoad()" style="
                background: #007cba;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                margin: 5px;
            ">重试加载地图</button>
            <button onclick="hideOfflineMode()" style="
                background: #6c757d;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                margin: 5px;
            ">继续使用</button>
        </div>
    `;
    offlineDiv.id = 'offline-mode';
    mapContainer.appendChild(offlineDiv);
}

// 重试加载地图
function retryMapLoad() {
    currentMapSourceIndex = 0;
    hideOfflineMode();
    loadMapLayer();
}

// 隐藏离线模式提示
function hideOfflineMode() {
    const offlineDiv = document.getElementById('offline-mode');
    if (offlineDiv) {
        offlineDiv.remove();
    }
}

// 解析CSV数据
function parseCSV(csvText) {
    const lines = csvText.split('\n');
    if (lines.length < 2) {
        showErrorMessage('CSV文件格式错误：文件为空或只有标题行');
        return [];
    }

    // 自动检测分隔符（制表符或逗号）
    const firstLine = lines[0];
    const separator = firstLine.includes('\t') ? '\t' : ',';
    console.log('检测到分隔符:', separator === '\t' ? '制表符' : '逗号');

    const headers = firstLine.split(separator);
    const data = [];

    console.log('CSV标题行:', headers);

    for (let i = 1; i < lines.length; i++) {
        if (lines[i].trim()) {
            const values = lines[i].split(separator);
            const row = {};
            headers.forEach((header, index) => {
                row[header.trim()] = values[index] ? values[index].trim() : '';
            });

            // 解析经纬度
            const coordText = row['故障系统（断点经纬度）'] || '';
            const lonMatch = coordText.match(/经度：([\d.]+)/);
            const latMatch = coordText.match(/纬度：([\d.]+)/);

            if (lonMatch && latMatch) {
                row.longitude = parseFloat(lonMatch[1]);
                row.latitude = parseFloat(latMatch[1]); // 正确：使用latMatch[1]获取纬度值

                // 只保留大理州范围内的数据 (大致范围)
                if (row.longitude >= 99 && row.longitude <= 101 &&
                    row.latitude >= 24.5 && row.latitude <= 27) {
                    data.push(row);
                }
            } else {
                // 如果没有找到经纬度格式，尝试其他可能的列名
                const lon = parseFloat(row['经度'] || row['longitude'] || row['lng'] || row['lon']);
                const lat = parseFloat(row['纬度'] || row['latitude'] || row['lat']);

                if (!isNaN(lon) && !isNaN(lat)) {
                    row.longitude = lon;
                    row.latitude = lat;

                    // 只保留大理州范围内的数据
                    if (row.longitude >= 99 && row.longitude <= 101 &&
                        row.latitude >= 24.5 && row.latitude <= 27) {
                        data.push(row);
                    }
                }
            }
        }
    }

    console.log('解析结果:', data.length, '条数据');
    if (data.length === 0) {
        showErrorMessage('未找到有效的地理坐标数据。请确保CSV文件包含经纬度信息。');
    }

    return data;
}

// 文件上传处理函数
function handleFileUpload(e) {
    const file = e.target.files[0];
    if (file) {
        console.log('选择的文件:', file.name, '大小:', file.size, '字节');
        const fileName = file.name.toLowerCase();

        if (fileName.endsWith('.csv')) {
            // 处理CSV文件
            console.log('开始处理CSV文件...');
            const reader = new FileReader();
            reader.onload = function(e) {
                console.log('文件读取完成，开始解析...');
                const csvText = e.target.result;
                console.log('CSV内容预览:', csvText.substring(0, 200) + '...');

                allData = parseCSV(csvText);
                filteredData = [...allData];

                console.log('解析完成，数据条数:', allData.length);

                if (allData.length > 0) {
                    displayData(filteredData);
                    initializeFilters();
                    applyFilters();
                    updateStats();
                    showSuccessMessage(`成功导入 ${allData.length} 条数据！`);
                } else {
                    showErrorMessage('文件解析失败或没有找到有效数据');
                }
            };
            reader.readAsText(file, 'UTF-8');
        } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
            // 处理Excel文件
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, {type: 'array'});
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];
                    const csvText = XLSX.utils.sheet_to_csv(worksheet);

                    allData = parseCSV(csvText);
                    filteredData = [...allData];
                    displayData(filteredData);
                    initializeFilters();
                    applyFilters();
                    updateStats();
                    showSuccessMessage(`成功导入 ${allData.length} 条数据！`);
                } catch (error) {
                    showErrorMessage('Excel文件解析失败: ' + error.message);
                }
            };
            reader.readAsArrayBuffer(file);
        } else {
            showErrorMessage('不支持的文件格式，请选择CSV或Excel文件');
        }
    }
}



// 加载示例数据
function loadSampleData() {
    // 从需求文档中提取部分示例数据
    const sampleData = [
        {
            '序号': '1',
            '区域': '宾川县',
            '故障系统（断点经纬度）': '经度：100.451122\n纬度：25.771616',
            '故障级别': '汇聚层',
            '故障段落': 'SPN 100G 环 宾川县 大营瑶草庄-宾川县州城',
            '故障发生时间': '2025/1/1 11:50',
            '故障原因': '车辆挂断',
            '故障详细原因备注': '瑶草庄出局3.2km挖机挂断光缆',
            longitude: 100.451122,
            latitude: 25.771616
        },
        {
            '序号': '2',
            '区域': '鹤庆县',
            '故障系统（断点经纬度）': '经度：100.157276\n纬度：26.538180',
            '故障级别': '汇聚层',
            '故障段落': 'OTN 老平面 鹤庆新移动（东北环）-洱源牛街上站',
            '故障发生时间': '2025/1/1 12:35',
            '故障原因': '车辆挂断',
            '故障详细原因备注': '新移动出局5.46km，附挂联通杆路超高车辆挂断',
            longitude: 100.157276,
            latitude: 26.538180
        },
        {
            '序号': '3',
            '区域': '弥渡县',
            '故障系统（断点经纬度）': '经度：100.454857\n纬度：25.336857',
            '故障级别': '接入层',
            '故障段落': 'PTN 弥渡县弥城双树村 6220设备脱管',
            '故障发生时间': '2025/1/1 13:40',
            '故障原因': '人为破坏',
            '故障详细原因备注': '蔡庄出局3km自来水厂修管道夹断导致',
            longitude: 100.454857,
            latitude: 25.336857
        },
        {
            '序号': '4',
            '区域': '洱源县',
            '故障系统（断点经纬度）': '经度：99.928195\n纬度：26.136207',
            '故障级别': '接入层',
            '故障段落': 'PTN GE链路 洱源县茈碧湖镇下汉登-洱源县茈碧湖基站',
            '故障发生时间': '2025/1/1 16:07',
            '故障原因': '车辆挂断',
            '故障详细原因备注': '下汉登基站出局1.97KM处超高车辆挂断',
            longitude: 99.928195,
            latitude: 26.136207
        },
        {
            '序号': '5',
            '区域': '大理市',
            '故障系统（断点经纬度）': '经度：100.203212\n纬度：25.593299',
            '故障级别': '接入层',
            '故障段落': 'PTN GE环 大理市下关龙泉新区华兴物业管理公司',
            '故障发生时间': '2025/1/2 16:49',
            '故障原因': '车辆挂断',
            '故障详细原因备注': '玉皇阁基站出局277米车辆挂断光缆',
            longitude: 100.203212,
            latitude: 25.593299
        }
    ];
    
    allData = sampleData;
    filteredData = [...allData];
    displayData(filteredData);
    initializeFilters();
    applyFilters();
    updateStats();
    showSuccessMessage(`成功加载示例数据 ${allData.length} 条！`);
}

// 初始化筛选器
function initializeFilters() {
    const regions = [...new Set(allData.map(item => item['区域']))].filter(Boolean);
    const levels = [...new Set(allData.map(item => item['故障级别']))].filter(Boolean);
    const causes = [...new Set(allData.map(item => item['故障原因']))].filter(Boolean);
    
    populateSelect('regionFilter', regions);
    populateSelect('levelFilter', levels);
    populateSelect('causeFilter', causes);
    
    updateStats();
}

// 填充下拉选择框
function populateSelect(selectId, options) {
    const select = document.getElementById(selectId);
    // 保留第一个选项（全部）
    const firstOption = select.children[0];
    select.innerHTML = '';
    select.appendChild(firstOption);
    
    options.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option;
        optionElement.textContent = option;
        select.appendChild(optionElement);
    });
}

// 应用筛选
function applyFilters() {
    const regionFilter = document.getElementById('regionFilter').value;
    const levelFilter = document.getElementById('levelFilter').value;
    const causeFilter = document.getElementById('causeFilter').value;
    const dateStart = document.getElementById('dateStart').value;
    const dateEnd = document.getElementById('dateEnd').value;
    const searchText = document.getElementById('searchInput').value.toLowerCase();
    
    filteredData = allData.filter(item => {
        // 区域筛选
        if (regionFilter && item['区域'] !== regionFilter) return false;
        
        // 故障级别筛选
        if (levelFilter && item['故障级别'] !== levelFilter) return false;
        
        // 故障原因筛选
        if (causeFilter && item['故障原因'] !== causeFilter) return false;
        
        // 时间筛选
        if (dateStart || dateEnd) {
            const itemDate = new Date(item['故障发生时间']);
            if (dateStart && itemDate < new Date(dateStart)) return false;
            if (dateEnd && itemDate > new Date(dateEnd)) return false;
        }
        
        // 搜索筛选
        if (searchText) {
            const searchFields = [
                item['故障段落'] || '',
                item['故障详细原因备注'] || ''
            ].join(' ').toLowerCase();
            
            if (!searchFields.includes(searchText)) return false;
        }
        
        return true;
    });
    
    updateMap();
    updateStats();
}

// 更新地图显示
function updateMap() {
    // 清除现有图层
    markersLayer.clearLayers();
    if (heatmapLayer) {
        map.removeLayer(heatmapLayer);
    }
    
    if (currentMapMode === 'markers') {
        showMarkers();
    } else {
        showHeatmap();
    }
}

// 显示标记
function showMarkers() {
    filteredData.forEach(item => {
        if (item.longitude && item.latitude) {
            const color = getColorByLevel(item['故障级别']);
            
            const marker = L.circleMarker([item.latitude, item.longitude], {
                radius: 8,
                fillColor: color,
                color: '#fff',
                weight: 2,
                opacity: 1,
                fillOpacity: 0.8
            });
            
            const popupContent = `
                <div class="popup-content">
                    <h4>${item['区域']} - ${item['故障级别']}</h4>
                    <p><strong>时间:</strong> ${item['故障发生时间']}</p>
                    <p><strong>原因:</strong> ${item['故障原因']}</p>
                    <p><strong>段落:</strong> ${item['故障段落']}</p>
                    <p><strong>详情:</strong> ${item['故障详细原因备注']}</p>
                    <p><strong>经纬度:</strong> ${item.latitude.toFixed(6)}, ${item.longitude.toFixed(6)}</p>
                </div>
            `;
            
            marker.bindPopup(popupContent);
            markersLayer.addLayer(marker);
        }
    });
}

// 显示热力图
function showHeatmap() {
    // 清除现有热力图层
    if (heatmapLayer) {
        map.removeLayer(heatmapLayer);
    }

    const heatData = filteredData
        .filter(item => item.longitude && item.latitude)
        .map(item => [item.latitude, item.longitude, 1]);

    console.log('热力图数据点数量:', heatData.length);
    console.log('热力图数据示例:', heatData.slice(0, 3));

    if (heatData.length > 0) {
        heatmapLayer = L.heatLayer(heatData, {
            radius: 30,
            blur: 20,
            maxZoom: 17,
            max: 1.0,
            gradient: {
                0.0: 'blue',
                0.4: 'cyan',
                0.6: 'lime',
                0.8: 'yellow',
                1.0: 'red'
            }
        }).addTo(map);

        console.log('热力图层已添加到地图');
    } else {
        console.log('没有有效的热力图数据');
    }
}

// 根据故障级别获取颜色
function getColorByLevel(level) {
    switch (level) {
        case '汇聚层': return '#ff4444';
        case '接入层': return '#44ff44';
        case '省干': return '#4444ff';
        default: return '#ffaa44';
    }
}

// 切换地图模式
function toggleMapMode(mode) {
    console.log('切换地图模式到:', mode);
    currentMapMode = mode;

    // 更新按钮状态
    document.querySelectorAll('.toggle-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // 根据模式文本找到对应按钮
    const targetBtn = Array.from(document.querySelectorAll('.toggle-btn')).find(btn => {
        return (mode === 'markers' && btn.textContent.includes('标记')) ||
               (mode === 'heatmap' && btn.textContent.includes('热力图'));
    });

    if (targetBtn) {
        targetBtn.classList.add('active');
    }

    updateMap();
}

// 执行搜索
function performSearch() {
    const searchInput = document.getElementById('searchInput');
    const searchResults = document.getElementById('searchResults');
    const searchText = searchInput.value.toLowerCase();
    
    if (searchText.length < 2) {
        searchResults.style.display = 'none';
        return;
    }
    
    const results = allData.filter(item => {
        const searchFields = [
            item['故障段落'] || '',
            item['故障详细原因备注'] || ''
        ].join(' ').toLowerCase();
        
        return searchFields.includes(searchText);
    }).slice(0, 10); // 限制显示10个结果
    
    if (results.length > 0) {
        searchResults.innerHTML = results.map(item => 
            `<div class="search-item" onclick="selectSearchResult('${item['序号']}')">${item['区域']} - ${item['故障段落']}</div>`
        ).join('');
        searchResults.style.display = 'block';
    } else {
        searchResults.style.display = 'none';
    }
}

// 选择搜索结果
function selectSearchResult(id) {
    const item = allData.find(d => d['序号'] === id);
    if (item && item.longitude && item.latitude) {
        map.setView([item.latitude, item.longitude], 15);
        document.getElementById('searchResults').style.display = 'none';
    }
}

// 清除筛选
function clearFilters() {
    document.getElementById('regionFilter').value = '';
    document.getElementById('levelFilter').value = '';
    document.getElementById('causeFilter').value = '';
    document.getElementById('dateStart').value = '';
    document.getElementById('dateEnd').value = '';
    document.getElementById('searchInput').value = '';
    document.getElementById('searchResults').style.display = 'none';
    
    applyFilters();
}

// 更新统计信息
function updateStats() {
    const totalCount = allData ? allData.length : 0;
    const filteredCount = filteredData ? filteredData.length : 0;

    document.getElementById('totalCount').textContent = totalCount;
    document.getElementById('filteredCount').textContent = filteredCount;

    console.log(`统计更新: 总数=${totalCount}, 显示=${filteredCount}`);
}

// 显示成功消息
function showSuccessMessage(message) {
    // 移除现有的消息
    removeMessages();

    const messageDiv = document.createElement('div');
    messageDiv.className = 'message success-message';
    messageDiv.innerHTML = `
        <div style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4caf50;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            z-index: 10000;
            font-weight: bold;
            max-width: 300px;
        ">
            ✅ ${message}
        </div>
    `;

    document.body.appendChild(messageDiv);

    // 3秒后自动消失
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 3000);
}

// 显示错误消息
function showErrorMessage(message) {
    // 移除现有的消息
    removeMessages();

    const messageDiv = document.createElement('div');
    messageDiv.className = 'message error-message';
    messageDiv.innerHTML = `
        <div style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f44336;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            z-index: 10000;
            font-weight: bold;
            max-width: 300px;
        ">
            ❌ ${message}
        </div>
    `;

    document.body.appendChild(messageDiv);

    // 5秒后自动消失
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 5000);
}

// 移除所有消息
function removeMessages() {
    const messages = document.querySelectorAll('.message');
    messages.forEach(msg => {
        if (msg.parentNode) {
            msg.parentNode.removeChild(msg);
        }
    });
}

// 导出数据
function exportData() {
    if (filteredData.length === 0) {
        alert('没有数据可导出');
        return;
    }
    
    const headers = ['序号', '区域', '故障级别', '故障发生时间', '故障原因', '故障段落', '故障详细原因备注'];
    const csvContent = [
        headers.join(','),
        ...filteredData.map(item => 
            headers.map(header => `"${item[header] || ''}"`).join(',')
        )
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = '故障数据筛选结果.csv';
    link.click();
}

// 网络检测
function checkNetworkConnectivity() {
    return new Promise((resolve) => {
        const img = new Image();
        const timeout = setTimeout(() => {
            resolve(false);
        }, 5000); // 5秒超时

        img.onload = () => {
            clearTimeout(timeout);
            resolve(true);
        };

        img.onerror = () => {
            clearTimeout(timeout);
            resolve(false);
        };

        // 使用一个小的图片来测试网络连接
        img.src = 'https://tile.openstreetmap.org/0/0/0.png?' + Date.now();
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    // 检查网络连接
    const hasNetwork = await checkNetworkConnectivity();
    if (!hasNetwork) {
        showMapStatus('网络连接检测失败，地图可能无法正常显示', 'error');
    }

    initMap();

    // 绑定文件上传事件
    const fileInput = document.getElementById('fileInput');
    console.log('查找fileInput元素:', fileInput);
    if (fileInput) {
        fileInput.addEventListener('change', handleFileUpload);
        console.log('文件上传事件已绑定到元素:', fileInput);

        // 测试点击事件
        fileInput.addEventListener('click', function() {
            console.log('文件选择器被点击');
        });
    } else {
        console.error('找不到fileInput元素');
        console.log('页面中的所有input元素:', document.querySelectorAll('input'));
    }

    // 点击其他地方隐藏搜索结果
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.search-box')) {
            const searchResults = document.getElementById('searchResults');
            if (searchResults) {
                searchResults.style.display = 'none';
            }
        }
    });
});
