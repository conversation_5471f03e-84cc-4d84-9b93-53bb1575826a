#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化测试文件上传功能
"""

import time
import subprocess
import threading
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import os

def start_server():
    """启动HTTP服务器"""
    try:
        # 启动Python HTTP服务器
        process = subprocess.Popen([
            'python', 'map_system_app.py'
        ], cwd=os.getcwd())
        
        # 等待服务器启动
        time.sleep(3)
        
        # 检查服务器是否启动成功
        try:
            response = requests.get('http://localhost:8080', timeout=5)
            print(f"✅ 服务器启动成功，状态码: {response.status_code}")
            return process
        except:
            print("❌ 服务器启动失败")
            return None
            
    except Exception as e:
        print(f"❌ 启动服务器时出错: {e}")
        return None

def test_file_upload():
    """测试文件上传功能"""
    print("🚀 开始自动化测试文件上传功能")
    
    # 启动服务器
    server_process = start_server()
    if not server_process:
        return False
    
    try:
        # 配置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # 无头模式
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        # 启动浏览器
        print("📱 启动浏览器...")
        driver = webdriver.Chrome(options=chrome_options)
        
        # 访问页面
        print("🌐 访问页面...")
        driver.get('http://localhost:8080')
        
        # 等待页面加载
        wait = WebDriverWait(driver, 10)
        
        # 检查页面标题
        print(f"📄 页面标题: {driver.title}")
        
        # 等待文件输入元素加载
        print("🔍 查找文件输入元素...")
        file_input = wait.until(
            EC.presence_of_element_located((By.ID, "fileInput"))
        )
        print("✅ 找到文件输入元素")
        
        # 检查测试文件是否存在
        test_file_path = os.path.abspath("test_upload.csv")
        if not os.path.exists(test_file_path):
            print(f"❌ 测试文件不存在: {test_file_path}")
            return False
        
        print(f"📁 测试文件路径: {test_file_path}")
        
        # 上传文件
        print("📤 上传文件...")
        file_input.send_keys(test_file_path)
        
        # 等待文件处理
        time.sleep(3)
        
        # 检查是否有成功提示
        print("🔍 检查页面反应...")
        
        # 执行JavaScript检查数据是否加载
        data_count = driver.execute_script("return window.allData ? window.allData.length : 0;")
        print(f"📊 加载的数据条数: {data_count}")
        
        # 检查地图标记
        markers_count = driver.execute_script("""
            return window.markersLayer ? window.markersLayer.getLayers().length : 0;
        """)
        print(f"🗺️ 地图标记数量: {markers_count}")
        
        # 检查控制台日志
        logs = driver.get_log('browser')
        print("📝 浏览器控制台日志:")
        for log in logs[-10:]:  # 显示最后10条日志
            print(f"   {log['level']}: {log['message']}")
        
        # 验证结果
        success = data_count > 0 and markers_count > 0
        
        if success:
            print("✅ 文件上传测试成功！")
            print(f"   - 数据条数: {data_count}")
            print(f"   - 地图标记: {markers_count}")
        else:
            print("❌ 文件上传测试失败！")
            
            # 获取更多调试信息
            page_source = driver.page_source
            if "fileInput" in page_source:
                print("✅ HTML中包含fileInput元素")
            else:
                print("❌ HTML中不包含fileInput元素")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理资源
        try:
            driver.quit()
            print("🔄 浏览器已关闭")
        except:
            pass
            
        try:
            server_process.terminate()
            print("🔄 服务器已停止")
        except:
            pass

def create_test_data():
    """创建测试数据文件"""
    test_data = """序号,区域,故障系统（断点经纬度）,故障级别,故障段落,故障发生时间,故障原因,故障详细原因备注
1,宾川县,"经度：100.451122
纬度：25.771616",汇聚层,SPN 100G 环 宾川县 大营瑶草庄-宾川县州城,2025/1/1 11:50,车辆挂断,瑶草庄出局3.2km挖机挂断光缆
2,鹤庆县,"经度：100.157276
纬度：26.538180",汇聚层,OTN 老平面 鹤庆新移动（东北环）-洱源牛街上站,2025/1/1 12:35,车辆挂断,新移动出局5.46km，附挂联通杆路超高车辆挂断
3,弥渡县,"经度：100.454857
纬度：25.336857",接入层,PTN 弥渡县弥城双树村 6220设备脱管,2025/1/1 13:40,人为破坏,蔡庄出局3km自来水厂修管道夹断导致"""
    
    with open("test_upload.csv", "w", encoding="utf-8") as f:
        f.write(test_data)
    
    print("✅ 测试数据文件已创建: test_upload.csv")

if __name__ == "__main__":
    print("=" * 60)
    print("大理州通信故障地图分析系统 - 文件上传自动化测试")
    print("=" * 60)
    
    # 创建测试数据
    create_test_data()
    
    # 运行测试
    success = test_file_upload()
    
    if success:
        print("\n🎉 测试通过！文件上传功能正常工作。")
    else:
        print("\n💥 测试失败！需要检查文件上传功能。")
    
    print("=" * 60)
