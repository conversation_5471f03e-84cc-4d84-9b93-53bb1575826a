<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>CSV解析测试</title>
</head>
<body>
    <h1>CSV解析测试</h1>
    <button onclick="testParseCSV()">测试CSV解析</button>
    <div id="result"></div>

    <script>
        // 复制app.js中的parseCSV函数
        function parseCSV(csvText) {
            const lines = csvText.split('\n');
            if (lines.length < 2) {
                alert('CSV文件格式错误：文件为空或只有标题行');
                return [];
            }
            
            // 自动检测分隔符（制表符或逗号）
            const firstLine = lines[0];
            const separator = firstLine.includes('\t') ? '\t' : ',';
            console.log('检测到分隔符:', separator === '\t' ? '制表符' : '逗号');
            
            const headers = firstLine.split(separator);
            const data = [];
            
            console.log('CSV标题行:', headers);
            
            for (let i = 1; i < lines.length; i++) {
                if (lines[i].trim()) {
                    const values = lines[i].split(separator);
                    const row = {};
                    headers.forEach((header, index) => {
                        row[header.trim()] = values[index] ? values[index].trim() : '';
                    });
                    
                    // 解析经纬度
                    const coordText = row['故障系统（断点经纬度）'] || '';
                    const lonMatch = coordText.match(/经度：([\d.]+)/);
                    const latMatch = coordText.match(/纬度：([\d.]+)/);
                    
                    if (lonMatch && latMatch) {
                        row.longitude = parseFloat(lonMatch[1]);
                        row.latitude = parseFloat(latMatch[1]); // 正确：使用latMatch[1]获取纬度值
                        
                        // 只保留大理州范围内的数据 (大致范围)
                        if (row.longitude >= 99 && row.longitude <= 101 && 
                            row.latitude >= 24.5 && row.latitude <= 27) {
                            data.push(row);
                        }
                    } else {
                        // 如果没有找到经纬度格式，尝试其他可能的列名
                        const lon = parseFloat(row['经度'] || row['longitude'] || row['lng'] || row['lon']);
                        const lat = parseFloat(row['纬度'] || row['latitude'] || row['lat']);
                        
                        if (!isNaN(lon) && !isNaN(lat)) {
                            row.longitude = lon;
                            row.latitude = lat;
                            
                            // 只保留大理州范围内的数据
                            if (row.longitude >= 99 && row.longitude <= 101 && 
                                row.latitude >= 24.5 && row.latitude <= 27) {
                                data.push(row);
                            }
                        }
                    }
                }
            }
            
            console.log('解析结果:', data.length, '条数据');
            if (data.length === 0) {
                alert('未找到有效的地理坐标数据。请确保CSV文件包含经纬度信息。');
            }
            
            return data;
        }

        function testParseCSV() {
            // 测试数据1：复杂格式
            const testData1 = `序号,区域,故障系统（断点经纬度）,故障级别,故障段落,故障发生时间,故障原因,故障详细原因备注
1,宾川县,"经度：100.451122 纬度：25.771616",汇聚层,SPN 100G 环 宾川县 大营瑶草庄-宾川县州城,2025/1/1 11:50,车辆挂断,瑶草庄出局3.2km挖机挂断光缆
2,鹤庆县,"经度：100.157276 纬度：26.538180",汇聚层,OTN 老平面 鹤庆新移动（东北环）-洱源牛街上站,2025/1/1 12:35,车辆挂断,新移动出局5.46km，附挂联通杆路超高车辆挂断`;

            // 测试数据2：简单格式
            const testData2 = `序号,区域,经度,纬度,故障级别,故障原因
1,大理市,100.203212,25.593299,接入层,车辆挂断
2,祥云县,100.5,25.5,汇聚层,设备故障`;

            console.log('=== 测试复杂格式 ===');
            const result1 = parseCSV(testData1);
            
            console.log('=== 测试简单格式 ===');
            const result2 = parseCSV(testData2);

            // 显示结果
            let html = '<h2>测试结果</h2>';
            html += '<h3>复杂格式CSV:</h3>';
            html += `<p>解析到 ${result1.length} 条数据</p>`;
            result1.forEach(item => {
                html += `<p>- ${item['区域']}: (${item.longitude}, ${item.latitude})</p>`;
            });

            html += '<h3>简单格式CSV:</h3>';
            html += `<p>解析到 ${result2.length} 条数据</p>`;
            result2.forEach(item => {
                html += `<p>- ${item['区域']}: (${item.longitude}, ${item.latitude})</p>`;
            });

            document.getElementById('result').innerHTML = html;
        }
    </script>
</body>
</html>
