#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的MCP文件上传功能测试
"""

import os
import re
import json
import time

class SimpleMCPTester:
    def __init__(self):
        self.test_results = []
    
    def create_test_files(self):
        """创建测试文件"""
        print("📁 创建测试文件...")
        
        # 有效的CSV文件
        valid_csv = """序号,区域,故障系统（断点经纬度）,故障级别,故障段落,故障发生时间,故障原因,故障详细原因备注
1,宾川县,"经度：100.451122 纬度：25.771616",汇聚层,SPN 100G 环 宾川县 大营瑶草庄-宾川县州城,2025/1/1 11:50,车辆挂断,瑶草庄出局3.2km挖机挂断光缆
2,鹤庆县,"经度：100.157276 纬度：26.538180",汇聚层,OTN 老平面 鹤庆新移动（东北环）-洱源牛街上站,2025/1/1 12:35,车辆挂断,新移动出局5.46km，附挂联通杆路超高车辆挂断
3,弥渡县,"经度：100.454857 纬度：25.336857",接入层,PTN 弥渡县弥城双树村 6220设备脱管,2025/1/1 13:40,人为破坏,蔡庄出局3km自来水厂修管道夹断导致"""
        
        with open("test_valid.csv", "w", encoding="utf-8") as f:
            f.write(valid_csv)
        
        # 简单格式CSV
        simple_csv = """序号,区域,经度,纬度,故障级别,故障原因
1,大理市,100.203212,25.593299,接入层,车辆挂断
2,祥云县,100.5,25.5,汇聚层,设备故障"""
        
        with open("test_simple.csv", "w", encoding="utf-8") as f:
            f.write(simple_csv)
        
        # 无效的CSV文件（没有经纬度）
        invalid_csv = """序号,区域,故障级别,故障原因
1,大理市,接入层,车辆挂断
2,祥云县,汇聚层,设备故障"""
        
        with open("test_invalid.csv", "w", encoding="utf-8") as f:
            f.write(invalid_csv)
        
        print("✅ 测试文件创建完成")
        return ["test_valid.csv", "test_simple.csv", "test_invalid.csv"]
    
    def test_csv_parsing_logic(self, csv_text, file_name):
        """测试CSV解析逻辑"""
        print(f"\n📊 测试CSV解析: {file_name}")
        
        try:
            lines = csv_text.split('\n')
            if len(lines) < 2:
                return {"success": False, "error": "文件为空或只有标题行", "data_count": 0}
            
            # 检测分隔符
            first_line = lines[0]
            separator = '\t' if '\t' in first_line else ','
            print(f"  检测到分隔符: {'制表符' if separator == '\t' else '逗号'}")
            
            headers = first_line.split(separator)
            print(f"  CSV标题行: {headers}")
            
            data_count = 0
            valid_data = []
            
            for i in range(1, len(lines)):
                if lines[i].strip():
                    values = lines[i].split(separator)
                    row = {}
                    for j, header in enumerate(headers):
                        row[header.strip()] = values[j].strip() if j < len(values) else ''
                    
                    # 解析经纬度
                    longitude = None
                    latitude = None
                    
                    # 检查复杂格式
                    coord_text = row.get('故障系统（断点经纬度）', '')
                    if coord_text:
                        lon_match = re.search(r'经度：([\d.]+)', coord_text)
                        lat_match = re.search(r'纬度：([\d.]+)', coord_text)
                        
                        if lon_match and lat_match:
                            longitude = float(lon_match.group(1))
                            latitude = float(lat_match.group(1))
                    
                    # 检查简单格式
                    if longitude is None or latitude is None:
                        lon_str = row.get('经度') or row.get('longitude') or row.get('lng') or row.get('lon')
                        lat_str = row.get('纬度') or row.get('latitude') or row.get('lat')
                        
                        if lon_str and lat_str:
                            try:
                                longitude = float(lon_str)
                                latitude = float(lat_str)
                            except:
                                pass
                    
                    # 验证坐标范围（大理州范围）
                    if longitude is not None and latitude is not None:
                        if 99 <= longitude <= 101 and 24.5 <= latitude <= 27:
                            data_count += 1
                            valid_data.append({
                                "区域": row.get('区域', ''),
                                "longitude": longitude,
                                "latitude": latitude,
                                "故障级别": row.get('故障级别', '')
                            })
                            print(f"    有效数据: {row.get('区域')} ({longitude}, {latitude})")
            
            print(f"  解析结果: {data_count} 条有效数据")
            
            if data_count > 0:
                return {"success": True, "error": None, "data_count": data_count, "data": valid_data}
            else:
                return {"success": False, "error": "未找到有效的地理坐标数据", "data_count": 0}
                
        except Exception as e:
            return {"success": False, "error": f"解析异常: {str(e)}", "data_count": 0}
    
    def test_ui_elements(self):
        """测试UI元素"""
        print("\n🖥️ 测试UI元素...")
        
        # 检查HTML文件
        if not os.path.exists("index.html"):
            print("❌ index.html文件不存在")
            return False
        
        with open("index.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        
        # 检查关键元素
        ui_checks = [
            ('id="fileInput"' in html_content, "文件输入元素"),
            ('选择文件' in html_content, "选择文件按钮"),
            ('id="totalCount"' in html_content, "总数统计元素"),
            ('id="filteredCount"' in html_content, "过滤数统计元素"),
            ('🧪 测试上传' not in html_content, "测试按钮已移除"),
            ('app.js' in html_content, "JavaScript文件引用")
        ]
        
        all_passed = True
        for check, desc in ui_checks:
            if check:
                print(f"  ✅ {desc}")
            else:
                print(f"  ❌ {desc}")
                all_passed = False
        
        self.test_results.append({
            "test": "UI元素检查",
            "status": "通过" if all_passed else "失败",
            "details": f"检查项: {len([c for c, _ in ui_checks if c])}/{len(ui_checks)}"
        })
        
        return all_passed
    
    def test_javascript_functions(self):
        """测试JavaScript函数"""
        print("\n📜 测试JavaScript函数...")
        
        if not os.path.exists("app.js"):
            print("❌ app.js文件不存在")
            return False
        
        with open("app.js", "r", encoding="utf-8") as f:
            js_content = f.read()
        
        # 检查关键函数
        js_checks = [
            ('function parseCSV' in js_content, "parseCSV函数"),
            ('function handleFileUpload' in js_content, "handleFileUpload函数"),
            ('function updateStats' in js_content, "updateStats函数"),
            ('function showSuccessMessage' in js_content, "showSuccessMessage函数"),
            ('function showErrorMessage' in js_content, "showErrorMessage函数"),
            ('testFileUpload' not in js_content, "测试函数已移除"),
            ('addEventListener.*change.*handleFileUpload' in js_content, "文件上传事件绑定")
        ]
        
        all_passed = True
        for check, desc in js_checks:
            if check:
                print(f"  ✅ {desc}")
            else:
                print(f"  ❌ {desc}")
                all_passed = False
        
        self.test_results.append({
            "test": "JavaScript函数检查",
            "status": "通过" if all_passed else "失败",
            "details": f"检查项: {len([c for c, _ in js_checks if c])}/{len(js_checks)}"
        })
        
        return all_passed
    
    def run_file_upload_tests(self):
        """运行文件上传测试"""
        print("\n📤 运行文件上传测试...")
        
        test_files = self.create_test_files()
        
        test_cases = [
            ("test_valid.csv", True, "复杂格式CSV（经度：xxx 纬度：xxx）"),
            ("test_simple.csv", True, "简单格式CSV（独立经纬度列）"),
            ("test_invalid.csv", False, "无效CSV（缺少经纬度）")
        ]
        
        all_passed = True
        
        for file_name, expected_success, description in test_cases:
            print(f"\n  📋 测试场景: {description}")
            
            with open(file_name, "r", encoding="utf-8") as f:
                csv_content = f.read()
            
            result = self.test_csv_parsing_logic(csv_content, file_name)
            
            test_passed = False
            if expected_success:
                if result['success'] and result['data_count'] > 0:
                    print(f"    ✅ 成功解析 {result['data_count']} 条数据")
                    test_passed = True
                else:
                    print(f"    ❌ 解析失败: {result['error']}")
            else:
                if not result['success']:
                    print(f"    ✅ 正确识别为无效: {result['error']}")
                    test_passed = True
                else:
                    print(f"    ❌ 应该失败但成功了")
            
            self.test_results.append({
                "test": f"文件上传-{description}",
                "status": "通过" if test_passed else "失败",
                "details": f"数据条数: {result['data_count']}, 错误: {result.get('error', '无')}"
            })
            
            if not test_passed:
                all_passed = False
        
        # 清理测试文件
        for file_name in test_files:
            if os.path.exists(file_name):
                os.remove(file_name)
        
        return all_passed
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 MCP自动化测试报告")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r["status"] == "通过"])
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {total_tests - passed_tests}")
        print(f"通过率: {(passed_tests/total_tests*100):.1f}%")
        
        print("\n详细结果:")
        for result in self.test_results:
            status_icon = "✅" if result["status"] == "通过" else "❌"
            print(f"  {status_icon} {result['test']}: {result['status']}")
            print(f"     {result['details']}")
        
        # 保存报告
        report_data = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "test_type": "MCP文件上传功能测试",
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "pass_rate": passed_tests/total_tests*100,
            "results": self.test_results
        }
        
        with open("mcp_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: mcp_test_report.json")
        
        return passed_tests == total_tests
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("🧪 MCP自动化文件上传功能测试")
        print("=" * 60)
        
        # 测试UI元素
        ui_passed = self.test_ui_elements()
        
        # 测试JavaScript函数
        js_passed = self.test_javascript_functions()
        
        # 测试文件上传逻辑
        upload_passed = self.run_file_upload_tests()
        
        # 生成报告
        all_passed = self.generate_report()
        
        return all_passed

def main():
    """主函数"""
    tester = SimpleMCPTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 所有MCP测试通过！文件上传功能完全正常。")
        print("\n✅ 验证结果:")
        print("  - 多余按钮已移除")
        print("  - 成功场景测试通过")
        print("  - 失败场景测试通过")
        print("  - UI元素检查通过")
        print("  - JavaScript函数检查通过")
        return 0
    else:
        print("\n💥 部分MCP测试失败！需要检查相关功能。")
        return 1

if __name__ == "__main__":
    exit(main())
