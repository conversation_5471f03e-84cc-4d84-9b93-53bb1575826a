<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大理州通信故障地图分析系统</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.css" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .container {
            display: flex;
            height: calc(100vh - 80px);
        }
        
        .sidebar {
            width: 350px;
            background: white;
            padding: 20px;
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }

        .map-status {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
            min-width: 200px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .map-status.info {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .map-status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        #map {
            height: 100%;
            width: 100%;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .control-group select,
        .control-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .control-group input[type="file"] {
            padding: 5px;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px 0;
            width: 100%;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .stats {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .stats h3 {
            color: #495057;
            margin-bottom: 10px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .search-box {
            position: relative;
        }
        
        .search-results {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-top: none;
            background: white;
            display: none;
        }
        
        .search-item {
            padding: 8px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
        }
        
        .search-item:hover {
            background: #f8f9fa;
        }
        
        .legend {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        
        .legend h4 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .popup-content {
            max-width: 300px;
        }
        
        .popup-content h4 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .popup-content p {
            margin-bottom: 5px;
            font-size: 12px;
        }
        
        .toggle-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .toggle-btn {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            text-align: center;
        }
        
        .toggle-btn.active {
            background: #667eea;
            color: white;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>大理州通信故障地图分析系统</h1>
        <p>故障点分布可视化与数据分析平台</p>
    </div>
    
    <div class="container">
        <div class="sidebar">
            <div class="control-group">
                <h3>📊 数据导入</h3>
                <label for="fileInput">选择数据文件 (CSV/Excel)</label>
                <input type="file" id="fileInput" accept=".csv,.xlsx,.xls" />
                <button class="btn" onclick="loadSampleData()">加载示例数据</button>
                <button class="btn" onclick="document.getElementById('fileInput').click()">📁 选择文件</button>
            </div>
            
            <div class="stats" id="stats">
                <h3>数据统计</h3>
                <div class="stat-item">
                    <span>总故障数:</span>
                    <span id="totalCount">0</span>
                </div>
                <div class="stat-item">
                    <span>当前显示:</span>
                    <span id="filteredCount">0</span>
                </div>
            </div>
            
            <div class="toggle-group">
                <div class="toggle-btn active" onclick="toggleMapMode('markers')">标记模式</div>
                <div class="toggle-btn" onclick="toggleMapMode('heatmap')">热力图</div>
            </div>
            
            <div class="control-group">
                <label for="regionFilter">区域筛选</label>
                <select id="regionFilter" onchange="applyFilters()">
                    <option value="">全部区域</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="levelFilter">故障级别</label>
                <select id="levelFilter" onchange="applyFilters()">
                    <option value="">全部级别</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="causeFilter">故障原因</label>
                <select id="causeFilter" onchange="applyFilters()">
                    <option value="">全部原因</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="dateStart">开始时间</label>
                <input type="date" id="dateStart" onchange="applyFilters()" />
            </div>
            
            <div class="control-group">
                <label for="dateEnd">结束时间</label>
                <input type="date" id="dateEnd" onchange="applyFilters()" />
            </div>
            
            <div class="control-group">
                <label for="searchInput">搜索故障段落/详细原因</label>
                <div class="search-box">
                    <input type="text" id="searchInput" placeholder="输入关键词搜索..." oninput="performSearch()" />
                    <div class="search-results" id="searchResults"></div>
                </div>
            </div>
            
            <button class="btn btn-secondary" onclick="clearFilters()">清除筛选</button>
            <button class="btn" onclick="exportData()">导出筛选结果</button>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
            <div class="legend">
                <h4>图例</h4>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff4444;"></div>
                    <span>汇聚层</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #44ff44;"></div>
                    <span>接入层</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #4444ff;"></div>
                    <span>省干</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ffaa44;"></div>
                    <span>其他</span>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
